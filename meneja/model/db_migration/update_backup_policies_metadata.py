# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, D<PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@


from meneja.business.backups import _attach_failure_email_templates_to_metadata
from meneja.business.g8.g8 import get_g8_jwt_from_db
from meneja.lib.clients.g8.client import G8ClientApiV1
from meneja.model.backups import Policies, Targets


def update_policies_metadata():
    all_policies = Policies.list()
    for policy in all_policies:
        policy_data = policy.to_mongo().to_dict()
        updated_metadata = _attach_failure_email_templates_to_metadata(
            policy_data["metadata"], policy_id=str(policy.id), policy_name=policy.name
        )
        for g8 in policy.subscribed_g8s:
            g8_client = G8ClientApiV1(g8.location, jwt=get_g8_jwt_from_db(g8.location))
            policy_payload = g8_client.backups.get_backup_policy(g8.id).to_dict()
            policy_payload["metadata"] = updated_metadata
            policy_payload.pop("id", None)
            g8_client.backups.update_backup_policy(g8.id, policy_payload)


def update_targets_metadata():
    all_targets = Targets.list()
    for target in all_targets:
        target_data = target.to_mongo().to_dict()
        target_data["metadata"]["vco_target_data"] = {
            "id": str(target["id"]),
            "name": target["name"],
        }
        for g8 in target.subscribed_g8s:
            g8_client = G8ClientApiV1(g8.location, jwt=get_g8_jwt_from_db(g8.location))
            target_payload = g8_client.backups.get_backup_target(g8.id).to_dict()
            if not target_payload["metadata"].get("vco_target_data"):
                target_payload["metadata"] = target_data["metadata"]
                target_payload["restic_password"] = target_data["restic_password"]
                target_payload["s3"]["access_key"] = target_data["s3"]["access_key"]
                target_payload["s3"]["secret_key"] = target_data["s3"]["secret_key"]
                target_payload.pop("id", None)
                g8_client.backups.update_backup_target(g8.id, target_payload)


def install():
    """
    This function is called by the main script to perform the database migration.
    It updates the metadata for backup policies and targets.
    """
    # update_policies_metadata()
    # update_targets_metadata()
    pass


if __name__ == "__main__":
    from meneja.lib.connection import MongoConnection

    MongoConnection.get_client()

    install()
