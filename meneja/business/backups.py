# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import binascii
import os
import re
import uuid
from dataclasses import asdict
from datetime import datetime
from typing import List, <PERSON>ple

import gevent
import jose
from jinja2 import Environment, FileSystemLoader, Template, Undefined
from werkzeug.exceptions import NotFound

from meneja.business.g8.cloudspace import create_cloudspace
from meneja.business.g8.g8_api import G8Client
from meneja.business.validation import (
    validate_cron_syntax,
    validate_email,
    validate_name,
    validate_password,
    validate_restic_retention_flags,
    validate_url,
)
from meneja.business.vco.customer.disks import get_disk_type
from meneja.lib.clients.g8.client import G8ClientApiV1
from meneja.lib.enumeration import BackupStatuses, EnvironmentName
from meneja.lib.utils import UrlsafeUnPaddedB64, get_meneja_url, send_email
from meneja.model.backups import (
    S3,
    AssignedPolicies,
    Metadata,
    NamedEntity,
    Policies,
    SnapshotPolicy,
    SubscribedLocation,
    Targets,
)
from meneja.model.g8_owners import G8Owner
from meneja.model.vco import VCO
from meneja.model.vco.customer import Customer, Location
from meneja.structs.vco.dataclasses.vco import (
    BackupCreateTargetStruct,
    BackupMetadataOwnerStruct,
    BackupMetadataStruct,
    BackupPolicyStruct,
    CreateVmFromBackupStruct,
)

GIG_NOTIFICATION_SENDER_EMAIL_ADDRESS = os.environ.get("GIG_NOTIFICATION_SENDER_EMAIL_ADDRESS", "<EMAIL>")
BACKUP_FAILURE_TEMPLATES_PATH = os.path.join(os.path.dirname(__file__), "vco/customer/templates")
CE_EMAIL_TEMPLATE = "ce_backup_failure_email.j2"
VCO_EMAIL_TEMPLATE = "vco_backup_failure_email.j2"


def list_repository_vms(jwt: str, target_id: str, customer_id: str = None) -> dict:
    """List repository VMs from the first G8 location that returns results.

    Args:
        jwt (str): JSON Web Token for authentication
        target_id (str): ID of the target
        customer_id (str, optional): Customer ID
    """
    target = Targets.get_by_id(_id=target_id, customer_id=customer_id)

    all_failed = True

    for g8 in target.subscribed_g8s:
        location = g8.location
        g8_target_id = g8.id
        g8_client = G8ClientApiV1(location, jwt=jwt)
        try:
            response = g8_client.backups.list_target_vms(target_id=g8_target_id)
            all_failed = False
            if response.data:
                result = [vm.to_dict() for vm in response.data]
                return {"result": result}
        except Exception as e:  # pylint: disable=broad-except
            print(f"Skipping location '{location}' (ID: {g8_target_id}) due to error: {e}")

    if all_failed:
        raise NotFound("All G8 locations failed to list repository VMs")

    return {"result": []}


def delete_vm_backups(jwt: str, target_id: str, vm_id: int, customer_id: str = None) -> None:
    """Delete VM backups from all subscribed G8 locations.

    Args:
        jwt (str): JWT
        target_id (str): Target ID
        vm_id (int): VM ID
        customer_id (str, optional): Customer id.
    """
    target = Targets.get_by_id(_id=target_id, customer_id=customer_id)

    for g8 in target.subscribed_g8s:
        location = g8.location
        g8_target_id = g8.id
        g8_client = G8ClientApiV1(location, jwt=jwt)

        try:
            g8_client.backups.delete_target_v_mbackups(target_id=g8_target_id, vm_id=vm_id)
            return {"success": True}
        except Exception as e:  # pylint: disable=broad-except
            print(f"Skipping location '{location}' (ID: {g8_target_id}) due to error: {e}")
            continue


def list_vm_backups(jwt: str, target_id: str, vm_id: int, customer_id: str = None):
    """List VM backups from subscribed G8 locations.

    Args:
        jwt (str): Authentication token
        target_id (str): Target ID
        vm_id (int): Virtual machine ID
        customer_id (str, optional): Customer ID. Defaults to None.
    """
    target = Targets.get_by_id(_id=target_id, customer_id=customer_id)
    local_policies = {}

    for g8 in target.subscribed_g8s:
        location = g8.location
        g8_target_id = g8.id
        g8_client = G8ClientApiV1(location, jwt=jwt)

        try:
            response = g8_client.backups.list_target_vm_backups(target_id=g8_target_id, vm_id=vm_id)

            backups = []
            for backup in response.data:
                dict_backup = backup.to_dict()
                dict_backup["cloudspace_id"] = _encode_base64_cloudspace_id(dict_backup["cloudspace_id"], location)

                policy_id = dict_backup.get("policy")
                if policy_id:
                    if local_policies.get(policy_id):
                        policy = local_policies[policy_id]
                    else:
                        policy = Policies.get_policy_by_subscribed_policy_id(policy_id, location=location)
                        if policy:
                            local_policies[policy_id] = policy
                        else:
                            policy = None

                    if policy:
                        dict_backup["policy"] = policy.id
                        dict_backup["policy_name"] = policy.name

                backups.append(dict_backup)

            return {"result": backups}

        except Exception as e:  # pylint: disable=broad-except
            print(f"Skipping location '{location}' (ID: {g8_target_id}) due to error: {e}")

    raise NotFound(f"No backups found for VM ID {vm_id} in any location.")


def get_vco_backup_settings(jwt: str, location: str) -> dict:
    """Get VCO backup settings"""
    claims = jose.jwt.get_unverified_claims(jwt)
    group_name = claims["iss"]
    g8_client = G8ClientApiV1(location, jwt=jwt)
    return g8_client.groups.get_group_metadata_settings(group_name)


def update_vco_backup_settings(vco: str, jwt: str, location: str, payload: dict) -> None:
    """Update VCO backup settings"""
    if not payload["allow_backup_override"]:
        targets = Targets.list(vco_id=vco, by_customer=True, location=location)
        if targets:
            raise ValueError(f"Cannot update backup settings: Targets subscribed to {location} exist.")
    claims = jose.jwt.get_unverified_claims(jwt)
    group_name = claims["iss"]
    g8_client = G8ClientApiV1(location, jwt=jwt)
    # for target in targets:
    #     for subscribed in target.subscribed_g8s:
    #         if subscribed.location == location:
    #             if "allow_backup_override" in payload:
    #                 subscribed.overridable = payload["allow_backup_override"]
    #     target.save()
    # policies = Policies.list(vco_id=group_name, by_customer=True)
    # for policy in policies:
    #     for subscribed in policy.subscribed_g8s:
    #         if subscribed.location == location:
    #             if "allow_backup_override" in payload:
    #                 subscribed.overridable = payload["allow_backup_override"]
    #     policy.save()
    g8_client.groups.set_group_metadata_settings(group_name, payload)
    return {"success": True}


def update_ce_backup_settings(jwt: str, location: str, payload: dict) -> None:
    """Update CE backup settings"""
    if not payload["allow_backup_override"]:
        targets = Targets.list(location=location)
        if targets:
            raise ValueError(f"Cannot update backup settings: Targets subscribed to {location} exists.")
    g8_client = G8ClientApiV1(location, jwt=jwt)
    # for target in targets:
    #     for subscribed in target.subscribed_g8s:
    #         if subscribed.location == location:
    #             if "allow_backup_override" in payload:
    #                 subscribed.overridable = payload["allow_backup_override"]
    #     target.save()
    # policies = Policies.list(vco_id=group_name, by_customer=True)
    # for policy in policies:
    #     for subscribed in policy.subscribed_g8s:
    #         if subscribed.location == location:
    #             if "allow_backup_override" in payload:
    #                 subscribed.overridable = payload["allow_backup_override"]
    #     policy.save()
    g8_client.locations.set_g8_location_settings(payload)
    return {"success": True}


def _create_backup_cloudspace(location, external_network_id: str, jwt: str) -> int:
    """Create Cloudspace for backup target

    Args:
        location (str): g8 name
        external_network_id (dict): external network id
        jwt (str): User Access JWT token

    Returns:
        dict: {"cloudspace_id": int}
    """
    kwargs = {}
    system_account = G8ClientApiV1(location, jwt).locations.get_get_system_account()
    kwargs["account_id"] = system_account.id
    kwargs["name"] = f"backup-CS-{location}-{external_network_id}-{uuid.uuid4()}"
    kwargs["external_network_id"] = external_network_id
    cloudspace = create_cloudspace(location, jwt, **kwargs)
    return int(cloudspace["cloudspace_id"])


def _delete_backup_cloudspace(location: str, cloudspace_id: int, jwt: str) -> None:
    """Delete backup cloudspace

    Args:
        location (str): g8 name
        cloudspace_id (int): Cloudspace ID
        jwt (str): JWT
    """

    G8Client(location, jwt=jwt).delete_cloudspace(
        cloudspace_id=cloudspace_id,
        permanently=True,
    )


def _add_ce_metadata(ce_id: str, backup_resource: dict) -> None:
    """Adds the cloud enabler metadata"""
    cloud_enabler = G8Owner.get_by_id(ce_id)
    vco_metadata_details = BackupMetadataOwnerStruct(name="", id="")
    cloud_enabler_metadata_details = BackupMetadataOwnerStruct(
        name=cloud_enabler.company_information.name, id=cloud_enabler.g8owner_id
    )
    customer_metadata_details = BackupMetadataOwnerStruct(name="", id="")
    metadata = BackupMetadataStruct(
        by_ce=True,
        by_vco_admin=False,
        by_customer=False,
        ce=cloud_enabler_metadata_details,
        vco=vco_metadata_details,
        customer=customer_metadata_details,
    )

    backup_resource["metadata"] = asdict(metadata)


def _add_vco_metadata(vco_id: str, backup_resource: dict) -> None:
    """Adds the vco metadata"""
    vco = VCO.get_by_id(vco_id)
    cloud_enabler = G8Owner.get_by_id(vco.g8owner_id)
    vco_metadata_details = BackupMetadataOwnerStruct(name=vco.name, id=vco_id)
    cloud_enabler_metadata_details = BackupMetadataOwnerStruct(
        name=cloud_enabler.company_information.name, id=cloud_enabler.g8owner_id
    )
    customer_metadata_details = BackupMetadataOwnerStruct(name="", id="")
    metadata = BackupMetadataStruct(
        by_ce=False,
        by_vco_admin=True,
        by_customer=False,
        ce=cloud_enabler_metadata_details,
        vco=vco_metadata_details,
        customer=customer_metadata_details,
    )

    backup_resource["metadata"] = asdict(metadata)


def _add_customer_metadata(vco_id: str, customer_id: str, backup_resource: dict) -> None:
    """Adds the customer metadata"""
    customer = Customer.get_by_id(customer_id)
    vco = VCO.get_by_id(vco_id)
    cloud_enabler = G8Owner.get_by_id(vco.g8owner_id)
    customer_metadata_details = BackupMetadataOwnerStruct(name=customer.company_information.name, id=customer_id)
    vco_metadata_details = BackupMetadataOwnerStruct(name=vco.name, id=vco_id)
    cloud_enabler_metadata_details = BackupMetadataOwnerStruct(
        name=cloud_enabler.company_information.name, id=cloud_enabler.g8owner_id
    )
    metadata = BackupMetadataStruct(
        by_ce=False,
        by_vco_admin=False,
        by_customer=True,
        customer=customer_metadata_details,
        vco=vco_metadata_details,
        ce=cloud_enabler_metadata_details,
    )
    backup_resource["metadata"] = asdict(metadata)


def _attach_failure_email_templates_to_metadata(metadata: dict, policy_id: str, policy_name: str) -> dict:
    """Adds the failure report email template to the metadata
    Args:
        metadata (dict): The metadata to update.
        policy_id (str): The ID of the backup policy.
        policy_name (str): The name of the backup policy.
    Returns:
        dict: The updated metadata with email templates.
    """

    class KeepPlaceholderUndefined(Undefined):
        def __str__(self):
            return f"{{{{ {self._undefined_name} }}}}"

        def __getattr__(self, name):
            return KeepPlaceholderUndefined(name=f"{self._undefined_name}.{name}")

    env = Environment(loader=FileSystemLoader(BACKUP_FAILURE_TEMPLATES_PATH), undefined=KeepPlaceholderUndefined)
    base_url = get_meneja_url()

    ce_email_template = _render_email_template(
        env, CE_EMAIL_TEMPLATE, base_url=base_url, policy_name=policy_name, policy_id=policy_id
    )
    vco_template = _render_email_template(env, VCO_EMAIL_TEMPLATE, policy_name=policy_name, policy_id=policy_id)
    metadata.update({"ce_email_template": ce_email_template, "vco_email_template": vco_template})
    return metadata


def _add_metadata(
    vco_id: str,
    customer_id: str,
    backup_resource: dict,
    by_customer: bool = False,
    by_ce: bool = False,
    ce_id: str = None,
) -> None:
    if by_ce:
        _add_ce_metadata(ce_id=ce_id, backup_resource=backup_resource)
    elif by_customer:
        _add_customer_metadata(vco_id, customer_id, backup_resource)
    else:
        _add_vco_metadata(vco_id, backup_resource)


def list_backup_policies(jwt: str, location: str) -> List[dict]:
    """
    Lists backup policies.

    Args:
        location (str): The location where the policies are applied.
        jwt (str): The JWT for authentication.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.

    Returns:
        List[dict]: A list of backup policies.
    """
    g8_client = G8ClientApiV1(location, jwt=jwt)
    response = g8_client.backups.list_backup_policy(limit=0)

    return response.data


def create_policy(
    policy_data: BackupPolicyStruct,
    vco_id: str = None,
    by_customer: bool = False,
    customer_id: str = None,
    jwt: str = None,
    by_ce: bool = False,
    ce_id: str = None,
):
    """_summary_

    Args:
        vco (str): _description_
        policy_data (BackupPolicyStruct): _description_
    """
    data = asdict(policy_data)
    validate_cron_syntax(data["cron"], "cron")
    validate_restic_retention_flags(data["restic_retention_flags"], "restic_retention_flags")
    validate_email(data["failure_report_email"], "failure_report_email")
    validate_name(data["name"], "name")
    _add_metadata(vco_id, customer_id, data, by_customer, by_ce, ce_id)
    snapshot_policy = data["snapshot_policy"]
    snapshot = SnapshotPolicy(
        cooperative=snapshot_policy["cooperative"],
        cooperative_timeout=snapshot_policy["cooperative_timeout"],
        cooperative_failure_behaviour=snapshot_policy["cooperative_failure_behaviour"],
        timeout=snapshot_policy["timeout"],
        retry_pause=snapshot_policy["retry_pause"],
        retry_times=snapshot_policy["retry_times"],
    )

    metadata_data = data["metadata"]
    metadata = Metadata(
        by_ce=metadata_data["by_ce"],
        by_vco_admin=metadata_data["by_vco_admin"],
        by_customer=metadata_data["by_customer"],
        ce=NamedEntity(**metadata_data["ce"]),
        vco=NamedEntity(**metadata_data["vco"]),
        customer=NamedEntity(**metadata_data["customer"]),
    )
    backup_policy = Policies(
        name=data["name"],
        subscribed_g8s=[],
        snapshot_policy=snapshot,
        cron=data["cron"],
        restic_retention_flags=data["restic_retention_flags"],
        failure_report_email=data["failure_report_email"],
        metadata=metadata,
        created_at=int(datetime.now().timestamp()),
        updated_at=int(datetime.now().timestamp()),
    )
    backup_policy.save()
    targets = Targets.list(vco_id=vco_id, by_customer=by_customer, customer_id=customer_id, by_ce=by_ce, ce_id=ce_id)
    for target in targets:
        if target.subscribed_g8s:
            assign_policy_to_target(backup_policy["id"], target["id"], jwt, customer_id)

    return backup_policy["id"]


def assign_policy_to_target(policy_id: str, target_id: str, jwt: str, customer_id: str = None):
    """
    Subscribe a policy to a target by creating a backup policy for each subscribed G8.

    Args:
        policy_id (str): The policy ID.
        target_id (str): The target ID.
        jwt (str): Authentication token.
        customer_id (str, optional): The ID of the customer.

    Raises:
        ValueError: If policy or target does not exist.
    """
    policy = Policies.get_by_id(_id=policy_id, customer_id=customer_id)
    target = Targets.get_by_id(_id=target_id, customer_id=customer_id)
    policy_data = policy.to_mongo().to_dict()
    target_data = target.to_mongo().to_dict()
    policy_data["metadata"] = _attach_failure_email_templates_to_metadata(
        policy_data["metadata"], policy_id=str(policy.id), policy_name=policy.name
    )
    policy_name_parts = [policy_data["name"], target_data["name"]]
    payload = {
        "name": "@".join(policy_name_parts),
        "cron": policy_data["cron"],
        "restic_retention_flags": policy_data["restic_retention_flags"],
        "failure_report_email": policy_data["failure_report_email"],
        "snapshot_policy": policy_data["snapshot_policy"],
        "target": None,
        "metadata": policy_data["metadata"],
    }

    if target.assigned_policies is None:
        target.assigned_policies = []

    if not target.subscribed_g8s:
        raise ValueError(f"Target {target_id} has no subscribed locations")

    new_assigned_policies = []
    new_policy_subscriptions = []

    for g8 in target.subscribed_g8s:
        payload["target"] = g8.id
        g8_client = G8ClientApiV1(g8.location, jwt=jwt)
        response = g8_client.backups.create_backup_policy(payload)

        assigned_policy = AssignedPolicies(
            policy_id=str(policy.id),
            subscribed_g8s=[SubscribedLocation(id=response.id, location=g8.location, name=response.name)],
        )
        new_assigned_policies.append(assigned_policy)
        new_policy_subscriptions.append(SubscribedLocation(id=response.id, location=g8.location, name=response.name))
    target.assigned_policies.extend(new_assigned_policies)
    target.save()

    if policy.subscribed_g8s is None:
        policy.subscribed_g8s = []
    policy.subscribed_g8s.extend(new_policy_subscriptions)
    policy.save()

    return {"success": True}


def unassign_policy_from_target(policy_id: str, target_id: str, jwt: str, customer_id: str = None):
    """
    Unsubscribe a policy from a target by removing the assigned policy for each subscribed G8.

    Args:
        policy_id (str): The policy ID.
        target_id (str): The target ID.
        jwt (str): Authentication token.
        customer_id (str, optional): The ID of the customer.

    Raises:
        ValueError: If policy or target does not exist, or if no assigned policies are found.
    """
    target = Targets.get_by_id(_id=target_id, customer_id=customer_id)
    policy = Policies.get_by_id(_id=policy_id, customer_id=customer_id)
    if not policy:
        raise ValueError(f"Policy {policy_id} not found")
    if not target:
        raise ValueError(f"Target {target_id} not found")

    if not target.assigned_policies:
        raise ValueError(f"Target {target_id} has no assigned policies")

    updated_policies = []
    removed_policies = []
    removed_policy_subscriptions = []

    for assigned_policy in target.assigned_policies:
        if assigned_policy.policy_id == policy_id:
            for g8 in assigned_policy.subscribed_g8s:
                g8_client = G8ClientApiV1(g8.location, jwt=jwt)
                g8_client.backups.delete_backup_policy(g8.id)
                removed_policy_subscriptions.append(g8)
            removed_policies.append(assigned_policy)
        else:
            updated_policies.append(assigned_policy)

    if not removed_policies:
        raise ValueError(f"Policy {policy_id} is not assigned to Target {target_id}")

    target.assigned_policies = updated_policies
    target.save()

    if policy.subscribed_g8s:
        policy.subscribed_g8s = [sub for sub in policy.subscribed_g8s if sub not in removed_policy_subscriptions]
        policy.save()

    return {"success": True}


def create_backup_policy(
    jwt: str,
    vco_id: str,
    location: str,
    policy_data: BackupPolicyStruct,
    by_customer: bool = False,
    customer_id: str = None,
) -> str:
    """
    Creates a new backup policy.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the policy should be applied.
        jwt (str): The JWT for authentication.
        policy_data (BackupPolicyStruct): The data for the backup policy.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.

    Returns:
        str: The ID of the created policy.
    """

    policy_data = asdict(policy_data)
    _add_metadata(vco_id, customer_id, policy_data, by_customer)
    g8_client = G8ClientApiV1(location, jwt=jwt)
    response = g8_client.backups.create_backup_policy(policy_data)
    return response.id


def get_backup_policy(jwt: str, location: str, policy_id: int) -> dict:
    """
    Retrieves a backup policy.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the policy is applied.
        jwt (str): The JWT for authentication.
        policy_id (str): The ID of the policy to retrieve.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.
        default_policy (bool): Whether to retrieve the default policy.

    Returns:
        dict: The retrieved backup policy.
    """

    g8_client = G8ClientApiV1(location, jwt=jwt)

    response = g8_client.backups.get_backup_policy(policy_id)
    policy = response.to_dict()

    return policy


def update_policy(
    jwt: str,
    vco_id: str,
    policy_id: str,
    payload: BackupPolicyStruct,
    by_customer: bool = False,
    customer_id: str = None,
) -> None:
    """Updates an existing backup policy in meneja DB, all subscribed locations"""
    policy: Policies = Policies.get_by_id(_id=policy_id, customer_id=customer_id)
    validate_cron_syntax(payload.cron, "cron")
    validate_restic_retention_flags(payload.restic_retention_flags, "restic_retention_flags")
    validate_email(payload.failure_report_email, "failure_report_email")
    validate_name(payload.name, "name")
    if policy.subscribed_g8s:
        for g8 in policy.subscribed_g8s:
            update_backup_policy(
                jwt=jwt,
                vco_id=vco_id,
                location=g8.location,
                policy_id=g8.id,
                policy_data=payload,
                by_customer=by_customer,
                customer_id=customer_id,
                vco_policy_id=policy_id,
                vco_policy_name=policy.name,
            )
            # temporary block for skipping db upgrade
            target_id = G8ClientApiV1(g8.location, jwt=jwt).backups.get_backup_policy(g8.id).target
            update_targets_metadata(target_id, jwt)

    policy.name = payload.name
    policy.cron = payload.cron
    policy.restic_retention_flags = payload.restic_retention_flags
    policy.failure_report_email = payload.failure_report_email
    policy.updated_at = int(datetime.now().timestamp())
    policy.snapshot_policy.cooperative = payload.snapshot_policy.cooperative
    policy.snapshot_policy.cooperative_timeout = payload.snapshot_policy.cooperative_timeout
    policy.snapshot_policy.cooperative_failure_behaviour = payload.snapshot_policy.cooperative_failure_behaviour
    policy.snapshot_policy.timeout = payload.snapshot_policy.timeout
    policy.snapshot_policy.retry_pause = payload.snapshot_policy.retry_pause
    policy.snapshot_policy.retry_times = payload.snapshot_policy.retry_times
    policy.save()


# temporary block for skipping db upgrade
def update_targets_metadata(target_id, jwt):
    """Update metadata for backup targets"""

    all_targets = Targets.list()
    for target in all_targets:
        for g8 in target.subscribed_g8s:
            if g8.id == target_id:
                target_data = target.to_mongo().to_dict()
                target_data["metadata"]["vco_target_data"] = {
                    "id": str(target["id"]),
                    "name": target["name"],
                }
                g8_client = G8ClientApiV1(g8.location, jwt=jwt)
                target_payload = g8_client.backups.get_backup_target(g8.id).to_dict()
                if not target_payload["metadata"].get("vco_target_data"):
                    target_payload["metadata"] = target_data["metadata"]
                    target_payload["restic_password"] = target_data["restic_password"]
                    target_payload["s3"]["access_key"] = target_data["s3"]["access_key"]
                    target_payload["s3"]["secret_key"] = target_data["s3"]["secret_key"]
                    target_payload.pop("id", None)
                    g8_client.backups.update_backup_target(g8.id, target_payload)


def update_backup_policy(
    jwt: str,
    vco_id: str,
    location: str,
    policy_id: int,
    policy_data: BackupPolicyStruct,
    by_customer: bool = False,
    customer_id: str = None,
    vco_policy_id: str = None,
    vco_policy_name: str = None,
) -> None:
    """
    Updates an existing backup policy.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the policy is applied.
        jwt (str): The JWT for authentication.
        policy_id (int): The ID of the policy to update.
        policy_data (BackupPolicyStruct): The new data for the backup policy.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.
    """

    policy_data = asdict(policy_data)
    _add_metadata(vco_id, customer_id, policy_data, by_customer)

    policy_data["metadata"] = _attach_failure_email_templates_to_metadata(
        policy_data["metadata"], vco_policy_id, vco_policy_name
    )

    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.backups.update_backup_policy(policy_id, policy_data)


def delete_backup_policy(jwt: str, location: str, policy_id: int) -> None:
    """
    Deletes a backup policy.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the policy is applied.
        jwt (str): The JWT for authentication.
        policy_id (str): The ID of the policy to delete.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.
    """

    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.backups.delete_backup_policy(policy_id)


def create_target(
    data: BackupCreateTargetStruct,
    vco_id: str = None,
    by_customer: bool = False,
    customer_id: str = None,
    by_ce: bool = False,
    ce_id: str = None,
) -> str:
    """Create target

    Args:
        vco_id (str): VCO ID
        data (BackupCreateTargetStruct): Target data
        by_customer (bool, optional): By customer. Defaults to False.
        customer_id (str, optional): Customer ID. Defaults to None.
    """
    data = asdict(data)
    _add_metadata(vco_id, customer_id, data, by_customer, by_ce, ce_id)
    s3_data = data["s3"]

    required_s3_fields = [
        "url",
        "region",
        "access_key",
        "secret_key",
        "bucket",
        "locking_mode",
    ]
    for field in required_s3_fields:
        if not s3_data.get(field):
            raise ValueError(f"S3 field '{field}' is required and cannot be empty.")
    validate_url(s3_data["url"], "url")
    validate_password(data["restic_password"], "Restic_password")
    validate_name(data["name"], "name")
    s3 = S3(
        url=s3_data["url"],
        region=s3_data["region"],
        access_key=s3_data["access_key"],
        secret_key=s3_data["secret_key"],
        bucket=s3_data["bucket"],
        locking_mode=s3_data["locking_mode"],
    )
    metadata_data = data["metadata"]
    metadata = Metadata(
        by_ce=metadata_data["by_ce"],
        by_vco_admin=metadata_data["by_vco_admin"],
        by_customer=metadata_data["by_customer"],
        ce=NamedEntity(**metadata_data["ce"]),
        vco=NamedEntity(**metadata_data["vco"]),
        customer=NamedEntity(**metadata_data["customer"]),
    )
    backup_target = Targets(
        name=data["name"],
        restic_password=data.get("restic_password", ""),
        s3=s3,
        metadata=metadata,
        subscribed_g8s=[],
        created_at=int(datetime.now().timestamp()),
        updated_at=int(datetime.now().timestamp()),
    )
    backup_target.save()
    return backup_target["id"]


def update_target(
    target_id: str,
    target_data: dict,
    by_customer: bool = False,
    customer_id: str = None,
    vco_id: str = None,
    by_ce: bool = False,
    ce_id: str = None,
) -> dict:
    """Update backup target"""

    target = Targets.get_by_id(target_id, customer_id=customer_id)
    if not target:
        raise ValueError(f"Target {target_id} not found.")
    if target.subscribed_g8s:
        raise ValueError(f"Cannot update target {target_id}: Active subscriptions exist.")
    target_data = asdict(target_data)
    _add_metadata(vco_id, customer_id, target_data, by_customer, by_ce, ce_id)
    s3_data = target_data.get("s3", {})
    required_s3_fields = [
        "url",
        "region",
        "access_key",
        "secret_key",
        "bucket",
        "locking_mode",
    ]
    for field in required_s3_fields:
        if not s3_data.get(field):
            raise ValueError(f"S3 field '{field}' is required and cannot be empty.")
    validate_url(s3_data["url"], "url")
    validate_password(target_data.get("restic_password", ""), "Restic_password")
    validate_name(target_data.get("name", ""), "name")
    target.name = target_data.get("name", target.name)
    target.restic_password = target_data.get("restic_password", target.restic_password)
    target.s3 = S3(
        url=s3_data["url"],
        region=s3_data["region"],
        access_key=s3_data["access_key"],
        secret_key=s3_data["secret_key"],
        bucket=s3_data["bucket"],
        locking_mode=s3_data["locking_mode"],
    )
    metadata_data = target_data.get("metadata", {})
    target.metadata = Metadata(
        by_ce=metadata_data.get("by_ce", target.metadata.by_ce),
        by_vco_admin=metadata_data.get("by_vco_admin", target.metadata.by_vco_admin),
        by_customer=metadata_data.get("by_customer", target.metadata.by_customer),
        ce=NamedEntity(**metadata_data.get("ce", {})),
        vco=NamedEntity(**metadata_data.get("vco", {})),
        customer=NamedEntity(**metadata_data.get("customer", {})),
    )
    target.updated_at = int(datetime.now().timestamp())
    target.save()

    return {"success": True}


def subscribe_backup_target(
    jwt: str,
    target_id: str,
    location: str,
    cloudspace_id: str = None,
    customer_id: str = None,
    vco_id: str = None,
    ce_id: str = None,
    externalnetwork_id: int = None,
    by_ce: bool = False,
) -> str:
    """
    Creates a new backup target and assigns all policies to the new subscribed location.

    Args:
        jwt (str): The JWT for authentication.
        target_id (str): The ID of the backup target.
        location (str): The location where the target should be applied.
        cloudspace_id (str): The cloudspace ID for the backup.

    Returns:
        str: The ID of the created target.
    """
    g8_client = G8ClientApiV1(location, jwt=jwt)
    target_data = Targets.get_by_id(_id=target_id, customer_id=customer_id)
    payload = target_data.to_mongo().to_dict()
    payload["metadata"]["vco_target_data"] = {
        "id": str(target_data["id"]),
        "name": target_data["name"],
    }
    if cloudspace_id:
        _cloudspace_id = _get_g8_cloudspace_id(cloudspace_id, location)
    elif externalnetwork_id:
        _cloudspace_id = _create_backup_cloudspace(location, externalnetwork_id, jwt)
    else:
        raise ValueError("Please provide cloudspace id!")
    target_name_parts = [payload["name"], ce_id, vco_id, customer_id]
    filtered_name_parts = [str(part) for part in target_name_parts if part is not None]
    target_payload = {
        "cloudspace_id": _cloudspace_id,
        "s3": payload["s3"],
        "restic_password": payload["restic_password"],
        "name": "@".join(filtered_name_parts),
        "metadata": payload["metadata"],
        "account_ids": [],
    }
    if customer_id:
        customer: Customer = Customer.get_by_id(customer_id)
        customer_location: Location
        for customer_location in customer.locations:
            if customer_location.location == location:
                break
        account_id = customer_location.g8_account_id
        target_payload["account_ids"].append(account_id)
    response = g8_client.backups.create_backup_target(target_payload)
    new_subscribed_location = SubscribedLocation(
        id=response.id,
        location=location,
        cloudspace_name=response.cloudspace_name,
        cloudspace_id=cloudspace_id,
        name=response.name,
    )
    target_data.subscribed_g8s.append(new_subscribed_location)

    # Retrieve all policies
    all_policies = Policies.list(customer_id=customer_id, by_ce=by_ce, ce_id=ce_id)

    for policy in all_policies:
        if any(
            str(policy.id) == target_policy.policy_id
            and location in [g8.location for g8 in target_policy.subscribed_g8s]
            for target_policy in target_data.assigned_policies
        ):
            continue

        policy_data = policy.to_mongo().to_dict()
        policy_data["metadata"] = _attach_failure_email_templates_to_metadata(
            policy_data["metadata"], policy_id=str(policy.id), policy_name=policy.name
        )
        policy_name_parts = [policy_data["name"], target_data["name"]]
        payload = {
            "name": "@".join(policy_name_parts),
            "cron": policy_data["cron"],
            "restic_retention_flags": policy_data["restic_retention_flags"],
            "failure_report_email": policy_data["failure_report_email"],
            "snapshot_policy": policy_data["snapshot_policy"],
            "target": response.id,
            "metadata": policy_data["metadata"],
        }

        policy_response = g8_client.backups.create_backup_policy(payload)
        new_policy_location = SubscribedLocation(id=policy_response.id, location=location, name=policy_response.name)
        policy.subscribed_g8s.append(new_policy_location)
        policy.save()

        does_policy_exists = False
        # save assigned policy in target data
        for assigned_policy in target_data.assigned_policies:
            if assigned_policy.policy_id == str(policy.id):
                assigned_policy.subscribed_g8s.append(new_policy_location)
                does_policy_exists = True
                break

        # if policy is not assigned to target, create a new one
        if not does_policy_exists:
            assigned_policy = AssignedPolicies(policy_id=str(policy.id), subscribed_g8s=[new_policy_location])
            target_data.assigned_policies.append(assigned_policy)
    target_data.save()

    return response.id


def unsubscribe_backup_target(
    jwt: str, target_id: str, location: str, customer_id: str = None, by_ce: bool = False
) -> str:
    """
    Unsubscribes a backup target from a specific location and removes assigned policies for that location.

    Args:
        jwt (str): The JWT for authentication.
        target_id (str): The ID of the target to unsubscribe from.
        location (str): The location to unsubscribe the target from.
        customer_id (str, optional): The customer ID.
    """
    target_data = Targets.get_by_id(
        _id=target_id,
        customer_id=customer_id,
    )
    matching_entry = next((sub for sub in target_data.subscribed_g8s if sub.location == location), None)
    if not matching_entry:
        raise ValueError(f"No subscription found for location '{location}' in target {target_id}")

    subscribed_id = matching_entry.id
    if by_ce:
        _delete_backup_cloudspace(
            location=matching_entry.id,
            cloudspace_id=_get_g8_cloudspace_id(matching_entry.cloudspace_id, location),
            jwt=jwt,
        )

    g8_client = G8ClientApiV1(location, jwt=jwt)
    target_name = target_data.name

    # Remove all assigned policies for the location
    if target_data.assigned_policies:
        updated_policies = []
        for assigned_policy in target_data.assigned_policies:
            policies_to_remove = []
            for g8 in assigned_policy.subscribed_g8s:
                if g8.location == location:
                    assigned_policy.subscribed_g8s.remove(g8)
                    policies_to_remove.append(g8.id)

            for policy_id in policies_to_remove:
                g8_client.backups.delete_backup_policy(policy_id)

            policy = Policies.get_by_id(_id=assigned_policy.policy_id, customer_id=customer_id)
            policy.subscribed_g8s = [
                sub
                for sub in policy.subscribed_g8s
                if sub.location != location or (sub.name.split("@")[1] if "@" in sub.name else sub.name) != target_name
            ]
            policy.save()

            if assigned_policy.subscribed_g8s:
                updated_policies.append(assigned_policy)

        target_data.assigned_policies = updated_policies

    # Delete the backup target
    g8_client.backups.delete_backup_target(subscribed_id)
    target_data.subscribed_g8s = [sub for sub in target_data.subscribed_g8s if sub.location != location]
    target_data.save()

    return {"success": True}


def sync_backup_target(jwt: str, location: str, target_id: str, customer_id: str = None) -> None:
    """
    Sync a backup target.

    Args:
        jwt (str): The JWT for authentication.
        location (str): The location where the target is applied.
        target_id (str): The ID of the target to retrieve.
        customer_id (str, optional): The ID of the customer.
    """
    target = Targets.get_by_id(target_id, customer_id=customer_id)
    matching_subscribed_g8 = next((sub for sub in target.subscribed_g8s if sub.location == location), None)

    if not matching_subscribed_g8:
        raise ValueError(f"No subscribed location '{location}' found for target {target_id}")

    subscribed_g8_target_id = matching_subscribed_g8.id
    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.backups.sync_target_backups(subscribed_g8_target_id)


def update_backup_target(
    jwt: str,
    vco_id: str,
    location: str,
    target_id: int,
    target_data: dict,
    by_customer: bool = False,
    customer_id: str = None,
) -> None:
    """
    Updates an existing backup target.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the target is applied.
        jwt (str): The JWT for authentication.
        target_id (int): The ID of the target to update.
        target_data (dict): The new data for the backup target.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.
    """

    target_data = asdict(target_data)
    _add_metadata(vco_id, customer_id, target_data, by_customer)

    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.backups.update_backup_target(target_id, target_data)


def get_location_target_id(target_id: str, location: str) -> int:
    """Get location target id"""
    target = Targets.get_by_id(_id=target_id)
    for g8 in target.subscribed_g8s:
        if g8.location == location:
            return g8.id


def delete_target(
    target_id: str,
    customer_id: str = None,
):
    """Delete target

    Args:
        target_id (str): Target ID
    """
    target_data = Targets.objects.get(id=target_id)
    if target_data.subscribed_g8s:
        raise ValueError(f"Cannot delete target {target_id}: Active subscriptions exist.")

    return Targets.delete_by_id(target_id, customer_id)


def delete_policy(
    policy_id: str,
    customer_id: str = None,
):
    """_summary_

    Args:
        policy_id (str): _description_
    """
    policy_data = Policies.objects.get(id=policy_id)
    if policy_data.subscribed_g8s:
        raise ValueError(f"Cannot delete policy {policy_id}: Active subscriptions exist.")
    return Policies.delete_by_id(_id=policy_id, customer_id=customer_id)


def delete_backup_target(jwt: str, location: str, target_id: str, customer_id: str = None) -> None:
    """
    Deletes a backup target.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the target is applied.
        jwt (str): The JWT for authentication.
        target_id (int): The ID of the target to delete.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.
    """

    target = Targets.get_by_id(target_id, customer_id)

    if target.subscribed_g8s:
        for subscribed_g8 in target.subscribed_g8s[:]:
            unsubscribe_backup_target(jwt, target_id, subscribed_g8.location, customer_id)

    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.backups.delete_backup_target(target_id)
    target.delete()


def list_backups(
    jwt: str,
    location: str,
    vm_id: int = None,
    policy: int = None,
    cloudspace_id: str = None,
    target_id: int = None,
    status: str = None,
    limit: int = 25,
    start_after: int = 0,
    exclude_expired: bool = True,
    sort_direction: int = -1,
    sort_by: str = "creation_timestamp",
) -> List[dict]:
    """
    Lists all backups in a VCO.

    Args:
        jwt (str): The JWT for authentication.
        location (str): The location where the G8 is running.
        vm_id (int, optional): The ID of the virtual machine.
        policy (int, optional): The ID of the backup policy.
        cloudspace_id (str, optional): The ID of the cloudspace.
        target_id (int, optional): The ID of the backup target.
        status (str, optional): The status of the backup.

    Returns:
        List[dict]: A list of backup dictionaries.
    """

    kwargs = {}

    if policy:
        kwargs["policy"] = policy
    if vm_id:
        kwargs["vm_id"] = vm_id
    if cloudspace_id:
        kwargs["cloudspace_id"] = _get_g8_cloudspace_id(cloudspace_id, location)
    if status:
        kwargs["status"] = status

    if target_id:
        target = Targets.get_by_id(target_id)
        matching_subscribed_g8 = next((sub for sub in target.subscribed_g8s if sub.location == location), None)

        if not matching_subscribed_g8:
            raise ValueError(f"Target {target_id} is not subscribed to location '{location}'")

        kwargs["target_id"] = matching_subscribed_g8.id
    if not target_id and not vm_id:
        raise ValueError("You must provide either target id or vm id to list backups")

    g8_client = G8ClientApiV1(location, jwt=jwt)
    if exclude_expired:
        kwargs["exclude_expired"] = exclude_expired

    response = g8_client.backups.list_backups(
        limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, **kwargs
    )

    backups = []
    local_policies = {}
    for backup in response.data:
        dict_backup = backup.to_dict()
        dict_backup["cloudspace_id"] = _encode_base64_cloudspace_id(dict_backup["cloudspace_id"], location)

        policy_id = dict_backup.get("policy")
        if policy_id != "" and policy_id is not None:
            if local_policies.get(policy_id):
                dict_backup["policy"] = local_policies[policy_id].id
                dict_backup["policy_name"] = local_policies[policy_id].name

            else:
                local_policy = Policies.get_policy_by_subscribed_policy_id(dict_backup["policy"], location=location)
                if local_policy:
                    local_policies[policy_id] = local_policy
                    dict_backup["policy"] = local_policy.id
                    dict_backup["policy_name"] = local_policy.name

        backups.append(dict_backup)
    return backups, response.pagination.count


def _send_failure_backup_email(
    customer_id: str,
    vm_name: str,
    cloudspace_name: str,
    start_time: int,
    failure_reason: str,
    failure_description: str,
    workflow_id: str,
) -> None:
    """
    Send backup failure notification email

    Args:
        customer_id (str): The ID of the customer.
        vm_name (str): Virtual machine name.
        cloudspace_name (str): cloudspace name.
        start_time (str): Backup start time.
        failure_reason (str): Reason of backup failure.
        failure_description (str): Description of backup failure.
    """

    customer = Customer.get_by_id(customer_id)
    vco = VCO.get_by_id(customer.vco, only=["name", "support_email", "g8owner_id"])
    cloudenabler_email = G8Owner.get_by_id(vco.g8owner_id).company_information.contact.email
    start_time = datetime.fromtimestamp(start_time).strftime("%Y-%m-%d %H:%M:%S")

    with open(f"{BACKUP_FAILURE_TEMPLATES_PATH}/backup_failure.j2", "r", encoding="utf8") as file_handle:
        template = Template(file_handle.read())
        message = template.render(
            vm_name=vm_name,
            cloudspace=cloudspace_name,
            vco_name=vco.name,
            start_time=start_time,
            failure_reason=failure_reason,
            failure_description=failure_description,
            customer_id=customer_id,
            workflow_id=workflow_id,
        )
        subject = f"Backup Failure Notification – Virtual Machine {vm_name}"
        job_send_to_ce = gevent.spawn(
            send_email,
            email=cloudenabler_email,
            subject=subject,
            message=message,
            send_from=GIG_NOTIFICATION_SENDER_EMAIL_ADDRESS,
        )
        job_send_to_customer = gevent.spawn(
            send_email,
            email=customer.company_information.contact.email,
            subject=subject,
            message=message,
            send_from=vco.support_email,
        )

        gevent.joinall([job_send_to_ce, job_send_to_customer], timeout=120)


def report_failed_backup(backup_id: str, customer_id: str, location: str, jwt: str) -> None:
    """
    Report backup failure

    Args:
        backup_id (str): The ID of the backup to retrieve.
        customer_id (str): The ID of the customer.
        location (str): The location where the G8 is running.
        jwt (str): The JWT for authentication.
    """

    backup = {}
    while backup.get("status", "") not in [
        BackupStatuses.FAILED,
        BackupStatuses.SUCCEEDED,
    ]:
        backup = get_backup(location=location, backup_id=backup_id, jwt=jwt)
        if backup["status"] == BackupStatuses.FAILED:
            _send_failure_backup_email(
                customer_id,
                backup["vm"]["name"],
                backup["cloudspace_name"],
                backup["start_time"],
                backup["failure_reason"],
                backup["failure_description"],
                backup["workflow_id"],
            )
        backup = get_backup(location=location, backup_id=backup_id, jwt=jwt)


def _render_email_template(env, template_name, **kwargs):
    template = env.get_template(template_name)
    html_template = template.render(**kwargs).replace("\n", "")
    return html_template


def create_backup(
    jwt: str,
    vco_id: str,
    location: str,
    vm_id: int,
    policy: int,
    by_customer: bool = False,
    customer_id: str = None,
) -> dict:
    """
    Creates a new backup in a VCO.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the G8 is running.
        jwt (str): The JWT for authentication.
        vm_id (int): The ID of the VM to backup.
        policy (int): The ID of the backup policy.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.
    Returns:
        dict: The created backup.
    """

    payload = dict(vm_id=vm_id, policy=policy)
    _add_metadata(vco_id, customer_id, payload, by_customer)

    g8_client = G8ClientApiV1(location, jwt=jwt)
    local_policy = Policies.get_policy_by_subscribed_policy_id(policy, location=location)
    policy_data = g8_client.backups.get_backup_policy(policy)
    local_target = Targets.get_target_by_subscribed_target_id(policy_data.target)

    payload["metadata"]["policy_name"] = policy_data.name
    if local_policy:
        payload["metadata"]["policy_id"] = str(local_policy.id)
        payload["metadata"]["target_id"] = str(local_target.id)

    response = g8_client.backups.create_backup(payload)
    backup = response.to_dict()
    backup["cloudspace_id"] = _encode_base64_cloudspace_id(backup["cloudspace_id"], location)

    if EnvironmentName.current() not in (EnvironmentName.DEV, EnvironmentName.TEST):
        gevent.spawn(report_failed_backup, backup["id"], customer_id, location, jwt)
    return backup


def get_backup(jwt: str, location: str, backup_id: str) -> dict:
    """
    Retrieves a backup from a VCO.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the G8 is running.
        jwt (str): The JWT for authentication.
        backup_id (str): The ID of the backup to retrieve.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.

    Returns:
        dict: The backup.
    """
    g8_client = G8ClientApiV1(location, jwt=jwt)
    response = g8_client.backups.get_backup(backup_id)
    backup = response.to_dict()
    backup["cloudspace_id"] = _encode_base64_cloudspace_id(backup["cloudspace_id"], location)
    policy_id = backup.get("policy")
    if policy_id != "" and policy_id is not None:
        local_policy = Policies.get_policy_by_subscribed_policy_id(backup["policy"], location=location)
        if local_policy:
            backup["policy"] = local_policy.id
            backup["policy_name"] = local_policy.name
    return backup


def delete_backup(jwt: str, location: str, backup_id: str) -> None:
    """
    Deletes a backup from a VCO.

    Args:
        vco_id (str): The ID of the VCO.
        location (str): The location where the G8 is running.
        jwt (str): The JWT for authentication.
        backup_id (str): The ID of the backup to delete.
        by_customer (bool): Whether to check the customer ID in the backup's metadata.
        customer_id (str): The ID of the customer.
    """

    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.backups.delete_backup(backup_id)


def create_vm_from_backup(jwt: str, location: str, cloudspace_id: str, backup_data: CreateVmFromBackupStruct) -> str:
    """
    Creates a new VM from a backup in a VCO.

    Args:
        vco_id (str): The ID of the VCO.
        customer_id (str): The ID of the customer.
        location (str): The location where the G8 is running.
        jwt (str): The JWT for authentication.
        cloudspace_id (str): The ID of the cloudspace.
        backup_data (CreateVmFromBackupStruct): The backup data.
    """

    backup_data = asdict(backup_data)
    backup_data["cloudspace_id"] = cloudspace_id

    g8_client = G8ClientApiV1(location, jwt=jwt)
    response = g8_client.virtual_machines.create_vm_from_backup(backup_data)
    response.backup_id = backup_data["backup_id"]
    return response


def vm_restore_progress(jwt: str, location: str, vm_id: int) -> dict:
    """
    Get the progress of a VM restore.

    Args:
        jwt (str): The JWT for authentication.
        location (str): G8 location.
        vm_id (int): The ID of the VM to restore.

    Returns:
        dict: The progress of the restore.
    """
    g8_client = G8ClientApiV1(location, jwt=jwt)
    response = g8_client.virtual_machines.get_vm_restore_progress(vm_id)
    for i, disk in enumerate(response.disks):
        response.disks[i].type = get_disk_type(disk.type, location, jwt)

    response.vm_id = vm_id
    return response


def assign_vm_backup_policy(jwt: str, location: str, vm_id: int, policy_id: int) -> None:
    """
    Assign a backup policy to a VM in a VCO.

    Args:
        vco_id (str): The ID of the VCO.
        customer_id (str): The ID of the customer.
        location (str): The location where the G8 is running.
        jwt (str): The JWT for authentication.
        vm_id (int): The ID of the VM to assign the policy to.
        policy_id (int): The ID of the backup policy to assign.
    """
    # policy = Policies.get_by_id(_id=policy_id, customer_id=customer_id)

    # policy_entry = next((sub for sub in policy.subscribed_g8s if sub["location"] == location), None)

    # if policy_entry:
    #     associated_policy_id = policy_entry["id"]
    # else:
    #     raise ValueError(f"No policy found for location '{location}' in policy {policy_id}")

    payload = dict(policy_id=policy_id)

    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.virtual_machines.assign_backup_policy(vm_id, payload)
    return {"success": True}


def unassign_vm_backup_policy(jwt: str, location: str, vm_id: int, policy_id: int) -> None:
    """
    Unassign a backup policy to a VM in a VCO.

    Args:
        location (str): The location where the G8 is running.
        jwt (str): The JWT for authentication.
        vm_id (int): The ID of the VM to assign the policy to.
        policy_id (int): The ID of the backup policy to assign.
    """

    g8_client = G8ClientApiV1(location, jwt=jwt)
    g8_client.virtual_machines.unassign_backup_policy(vm_id=vm_id, policy_id=policy_id)
    return {"success": True}


def _decode_validate_cloudspace_id(cloudspace_id) -> Tuple[str, int]:
    """Decoded and validate cloudspace_id that is in base64 format with location

    Raises:
        ValueError: Id does not match location:id format

    Returns:
        Tuple[str, int]: Tuple of the location and the cloudspace ID in that location
    """
    cs_id_pattern = re.compile(r"^((?!-)[A-Za-z0-9-]+(?<!-)):([0-9]+)$")

    try:
        match = cs_id_pattern.fullmatch(UrlsafeUnPaddedB64.decode(cloudspace_id))
    except (binascii.Error, UnicodeDecodeError) as exp:
        raise ValueError("Invalid cloudspace_id") from exp
    if match is None:
        raise ValueError("Invalid cloudspace_id")
    location, cloudspace_id = match.groups()
    return location, int(cloudspace_id)


def _encode_base64_cloudspace_id(cloudspace_id: int, location: str) -> str:
    """Encode cloudspace id into location:cloudspace_id in base64

    Args:
        cloudspace_id (int): raw ID of the cloudspace
        location (str): Location

    Returns:
        str: location encoded with the cloudspace_id
    """
    return UrlsafeUnPaddedB64.encode(f"{location}:{cloudspace_id}")


def _get_g8_cloudspace_id(cloudspace_id, location) -> int:
    cloudspace_location, g8_cloudspace_id = _decode_validate_cloudspace_id(cloudspace_id)

    if cloudspace_location != location:
        raise ValueError("Invalid Cloudspace id.")
    return g8_cloudspace_id
