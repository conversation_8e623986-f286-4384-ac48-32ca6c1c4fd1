# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
import time
from typing import Dict, Optional

import requests
from flask import current_app
from flask_itsyouonline import get_current_user_info

from meneja.lib.enumeration import EnvironmentName
from meneja.lib.k8 import patch_vco_login
from meneja.model.vco import VCO
from meneja.structs.vco.dataclasses.oidc_provider import OIDCProviderIdStruct


def get_iam_base_url(vco_id):
    """
    Get the base URL for the IAM service

    Args:
        vco_id (str): VCO ID

    Returns:
        str: Base URL for the IAM service
    """
    vco = VCO.get_by_id(vco_id, only=["iam_domain"])
    is_https = EnvironmentName.current() != EnvironmentName.TEST
    protocol = "https" if is_https else "http"
    return f"{protocol}://{vco.iam_domain}"


def get_oidc_provider_logs(vco_id: str) -> Dict:
    """Get OIDC provider logs from the IAM service

    Args:
        vco_id (str): VCO ID

    Returns:
        Dict: OIDC provider logs with version and messages
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.get(
        f"{base_url}/admin/oidc_provider_logs", headers={"Authorization": f"bearer {jwt}"}, timeout=120
    )

    if response.status_code != 200:
        current_app.logger.error(f"Failed to get OIDC provider logs: {response.text}")
        response.raise_for_status()

    return response.json()


def check_new_oidc_logs(vco_id: str, version: int, timeout: int = 60) -> Optional[Dict]:
    """Check OIDC provider logs until the version matches or timeout is reached

    Args:
        vco_id (str): VCO ID
        version (int): Expected version number
        timeout (int, optional): Timeout in seconds. Defaults to 60.

    Returns:
        Optional[Dict]: OIDC provider logs if version matches, None if timeout reached
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        logs = get_oidc_provider_logs(vco_id)
        if logs.get("oidcVersion") == version:
            return logs
        time.sleep(5)

    return None


def list_oidc_providers(vco_id, only_active=False):
    """
    List OIDC providers from the IAM service

    Args:
        vco_id (str): VCO ID
        only_active (bool): Whether to only return active providers

    Returns:
        list: List of OIDC providers
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    url = f"{base_url}/admin/oidc_providers"
    if only_active:
        url += "?active=true"

    response = requests.get(url, headers={"Authorization": f"bearer {jwt}"}, timeout=120)

    if response.status_code != 200:
        current_app.logger.error(f"Failed to list OIDC providers: {response.text}")
        response.raise_for_status()

    return response.json().get("results", [])


def get_oidc_provider(vco_id, provider_id):
    """
    Get an OIDC provider by ID

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        dict: OIDC provider details
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.get(
        f"{base_url}/admin/oidc_providers/{provider_id}", headers={"Authorization": f"bearer {jwt}"}, timeout=120
    )

    if response.status_code == 404:
        return None

    if response.status_code != 200:
        current_app.logger.error(f"Failed to get OIDC provider: {response.text}")
        response.raise_for_status()

    return response.json()


def create_oidc_provider(vco_id: str, provider_data: dict):
    """Create a new OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_data (dict): OIDC provider configuration

    Returns:
        dict: Created OIDC provider details
    """
    current_logs = get_oidc_provider_logs(vco_id)
    new_version = current_logs.get("oidcVersion", 0) + 1

    vco = VCO.get_by_id(vco_id)
    iam_name = vco.domain.replace(".", "-")
    patch_vco_login(
        iam_name=iam_name,
        oidc_provider=provider_data,
        oidc_provider_action="create",
        oidc_provider_version=new_version,
    )

    logs = check_new_oidc_logs(vco_id, new_version)
    if not logs:
        raise TimeoutError("Timeout waiting for OIDC provider creation")
    if not logs.get("oidcActionSucceeded", False):
        raise ValueError(f"Failed to create OIDC provider: {logs.get('oidcError', '')}")

    provider_id = logs.get("oidcProviderId")
    if not provider_id:
        raise ValueError("Failed to get the new OIDC provider data.")

    return OIDCProviderIdStruct(id=provider_id)


def update_oidc_provider(vco_id: str, provider_id: str, provider_data: dict):
    """Update an existing OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID
        provider_data (dict): Updated OIDC provider configuration

    Returns:
        dict: Updated OIDC provider details
    """
    current_logs = get_oidc_provider_logs(vco_id)
    new_version = current_logs.get("oidcVersion", 0) + 1

    vco = VCO.get_by_id(vco_id)
    iam_name = vco.domain.replace(".", "-")
    patch_vco_login(
        iam_name=iam_name,
        oidc_provider=provider_data,
        oidc_provider_id=provider_id,
        oidc_provider_action="update",
        oidc_provider_version=new_version,
    )

    logs = check_new_oidc_logs(vco_id, new_version)
    if not logs:
        raise TimeoutError("Timeout waiting for OIDC provider update")
    if not logs.get("oidcActionSucceeded", False):
        raise ValueError(f"Failed to update OIDC provider: {logs.get('oidcError', '')}")
    return get_oidc_provider(vco_id, provider_id)


def delete_oidc_provider(vco_id: str, provider_id: str):
    """Delete an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Raises:
        Exception: If provider deletion fails
    """
    current_logs = get_oidc_provider_logs(vco_id)
    new_version = current_logs.get("oidcVersion", 0) + 1

    vco = VCO.get_by_id(vco_id)
    iam_name = vco.domain.replace(".", "-")
    patch_vco_login(
        iam_name=iam_name,
        oidc_provider_id=provider_id,
        oidc_provider_action="delete",
        oidc_provider_version=new_version,
    )

    logs = check_new_oidc_logs(vco_id, new_version)
    if not logs:
        raise TimeoutError("Timeout waiting for OIDC provider deletion")
    if not logs.get("oidcActionSucceeded", False):
        raise ValueError(f"Failed to delete OIDC provider: {logs.get('oidcError', '')}")


def activate_oidc_provider(vco_id: str, provider_id: str):
    """Activate an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Raises:
        Exception: If provider activation fails
    """
    current_logs = get_oidc_provider_logs(vco_id)
    new_version = current_logs.get("oidcVersion", 0) + 1

    vco = VCO.get_by_id(vco_id)
    iam_name = vco.domain.replace(".", "-")
    patch_vco_login(
        iam_name=iam_name,
        oidc_provider_id=provider_id,
        oidc_provider_action="activate",
        oidc_provider_version=new_version,
    )

    logs = check_new_oidc_logs(vco_id, new_version)
    if not logs:
        raise TimeoutError("Timeout waiting for OIDC provider activation")
    if not logs.get("oidcActionSucceeded", False):
        raise ValueError(f"Failed to activate OIDC provider: {logs.get('oidcError', '')}")


def deactivate_oidc_provider(vco_id: str, provider_id: str):
    """Deactivate an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Raises:
        Exception: If provider deactivation fails
    """
    current_logs = get_oidc_provider_logs(vco_id)
    new_version = current_logs.get("oidcVersion", 0) + 1

    vco = VCO.get_by_id(vco_id)
    iam_name = vco.domain.replace(".", "-")
    patch_vco_login(
        iam_name=iam_name,
        oidc_provider_id=provider_id,
        oidc_provider_action="deactivate",
        oidc_provider_version=new_version,
    )

    logs = check_new_oidc_logs(vco_id, new_version)
    if not logs:
        raise TimeoutError("Timeout waiting for OIDC provider deactivation")
    if not logs.get("oidcActionSucceeded", False):
        raise ValueError(f"Failed to deactivate OIDC provider: {logs.get('oidcError', '')}")


def get_password_login_status(vco_id):
    """
    Get the current status of password login

    Args:
        vco_id (str): VCO ID

    Returns:
        dict: Password login status
    """
    base_url = get_iam_base_url(vco_id)
    jwt = get_current_user_info().jwt

    response = requests.get(
        f"{base_url}/password_login/status", headers={"Authorization": f"bearer {jwt}"}, timeout=120
    )
    if response.status_code != 200:
        current_app.logger.error(f"Failed to get password login status: {response.text}")
        response.raise_for_status()

    return response.json()


def enable_password_login(vco_id):
    """
    Enable password-based login

    Args:
        vco_id (str): VCO ID


    Raises:
        Exception: If password login enable fails
    """
    current_logs = get_oidc_provider_logs(vco_id)
    new_version = current_logs.get("oidcVersion", 0) + 1

    vco = VCO.get_by_id(vco_id)
    iam_name = vco.domain.replace(".", "-")
    patch_vco_login(iam_name=iam_name, password_login_action="enable", oidc_provider_version=new_version)

    logs = check_new_oidc_logs(vco_id, new_version)
    if not logs:
        raise TimeoutError("Timeout waiting for password login enable")
    if not logs.get("passwordLoginActionSucceeded", False):
        raise ValueError(f"Failed to enable password login: {logs.get('passwordLoginError', '')}")


def disable_password_login(vco_id):
    """
    Disable password-based login

    Args:
        vco_id (str): VCO ID

    Raises:
        Exception: If password login disable fails
    """
    current_logs = get_oidc_provider_logs(vco_id)
    new_version = current_logs.get("oidcVersion", 0) + 1

    vco = VCO.get_by_id(vco_id)
    iam_name = vco.domain.replace(".", "-")
    patch_vco_login(iam_name=iam_name, password_login_action="disable", oidc_provider_version=new_version)

    logs = check_new_oidc_logs(vco_id, new_version)
    if not logs:
        raise TimeoutError("Timeout waiting for password login disable")
    if not logs.get("passwordLoginActionSucceeded", False):
        raise ValueError(f"Failed to disable password login: {logs.get('passwordLoginError', '')}")
