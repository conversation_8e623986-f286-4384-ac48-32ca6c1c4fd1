# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON><PERSON>OSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import logging
from os import environ
from time import time

import requests
from dynaqueue.scheduler import schedule
from dynaqueue.worker.context import get_context

from meneja.jobs import job
from meneja.lib.utils import rocket_chat_channel, rocket_chat_message
from meneja.model.g8_owners import G8Owner
from meneja.model.healthchecks import EmailsExport
from meneja.model.vco import VCO
from meneja.model.vco.customer import Customer

logger = logging.getLogger()


@schedule("0 6 * * *", "Collect customer emails")
def schedule_collect_customers_emails():
    """Schedule exporting customer emails"""
    collection_timestamp = int(time())
    email_export = EmailsExport()
    email_export.timestamp = collection_timestamp
    email_export.failed_jobs = []
    email_export.save()
    _collect_customer_emails(collection_timestamp=collection_timestamp)
    _collect_admin_emails(collection_timestamp=collection_timestamp)


def _clean_up(*args, **kwargs) -> None:
    """Clean up after failed emails export fail"""
    _update_health_check(*args, **kwargs)
    rocket_chat_message(channel_id=rocket_chat_channel(), message="Failed to export emails")


@job(
    title="Export Customer emails",
    object_type="exportEmails",
    object_id="exportEmails",
    cleanup=_clean_up,
    cleanup_title="Clean up after failed exporting customer emails",
    retry_count=1,
    timeout=3 * 24 * 3600,
)
def _collect_customer_emails(collection_timestamp: int):
    """Collect customer emails for communication when portal is unaccessible"""
    logger.info("Starting exports of customer emails (%s)", collection_timestamp)

    if not environ.get("EMAIL_SERVER_URL"):
        logger.info("Email server url is not set")
        return
    emails = []
    for customer in Customer.list():
        emails.append(customer.company_information.contact.email)
    resp = requests.post(
        url=f'https://{environ.get("EMAIL_SERVER_URL")}/customer-emails', json={"emails": emails}, timeout=300
    )
    resp.raise_for_status()
    logger.info("Successfuly exported all customer emails!")
    logger.exception("Failed to export customer emails")


@job(
    title="Export admin emails",
    object_type="exportEmails",
    object_id="exportEmails",
    cleanup=_clean_up,
    cleanup_title="Clean up after failed exporting admin emails",
    retry_count=1,
    timeout=3 * 24 * 3600,
)
def _collect_admin_emails(collection_timestamp: int):
    """Collect admin (VCO and CE) emails for communication when portal is unaccessible"""
    logger.info("Starting exports of admin emails (%s)", collection_timestamp)

    if not environ.get("EMAIL_SERVER_URL"):
        logger.info("Email server url is not set")
        return
    emails = []
    for g8_owner in G8Owner.list():
        emails.append(g8_owner.company_information.contact.email)
    for vco in VCO.list(active_only=True):
        emails.append(vco.company_information.contact.email)

    resp = requests.post(
        url=f'https://{environ.get("EMAIL_SERVER_URL")}/admin-emails', json={"emails": emails}, timeout=300
    )
    resp.raise_for_status()
    logger.info("Successfuly exported all admin emails!")


def _update_health_check(*_, **kwargs) -> None:
    collection_timestamp = kwargs.get("collection_timestamp")
    context = get_context()
    email_export = EmailsExport.get_by_id(collection_timestamp)
    email_export.failed_jobs.append(context.status.id)
    email_export.save()
